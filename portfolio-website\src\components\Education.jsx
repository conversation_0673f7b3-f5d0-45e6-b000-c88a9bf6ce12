const Education = () => {
  const educationData = [
    {
      degree: "Bachelor's in Computer Systems Engineering",
      institution: "Dawood University of Engineering & Technology",
      location: "Karachi, Pakistan",
      duration: "2021 - 2025",
      cgpa: "3.3/4.0",
      status: "Expected Graduation: 2025",
      description: "Comprehensive program covering computer systems, software engineering, and emerging technologies",
      coursework: [
        "Data Structures & Algorithms",
        "Database Management Systems",
        "Software Engineering",
        "Computer Network Security",
        "Operating Systems",
        "Web Engineering",
        "Artificial Intelligence",
        "Data Science",
        "Computer Architecture",
        "DigitalSystem Design"
      ],
      achievements: [
        "Maintained consistent academic performance",
        "Completed multiple technical projects",
        "Active participation in coding competitions",
        "Final year project on AI-based medical imaging"
      ]
    },
    {
      degree: "Intermediate in Pre-Engineering",
      institution: "DJ Sindh Government Science College",
      location: "Karachi, Pakistan",
      duration: "2017 - 2019",
      percentage: "94%",
      status: "Completed",
      description: "Strong foundation in mathematics, physics, and chemistry with excellent academic performance",
      coursework: [
        "Mathematics",
        "Physics",
        "Chemistry",
      ],
      achievements: [
        "Achieved 94% marks - Excellent performance",
        "Strong foundation in STEM subjects",
        "Developed analytical and problem-solving skills",
        "Prepared for engineering entrance exams"
      ]
    }
  ];



  return (
    <section id="education" className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">Education & Learning</h2>
          <div className="w-24 h-1 bg-primary-500 mx-auto mb-6"></div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            My academic journey and continuous learning in technology and engineering
          </p>
        </div>

        {/* Education Timeline */}
        <div className="space-y-12 mb-16">
          {educationData.map((edu, index) => (
            <div
              key={index}
              className="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-all duration-300"
            >
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                {/* Main Info */}
                <div className="lg:col-span-2">
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h3 className="text-2xl font-bold text-gray-900 mb-2">{edu.degree}</h3>
                      <p className="text-xl text-primary-600 font-semibold mb-1">{edu.institution}</p>
                      <p className="text-gray-600 mb-2">{edu.location}</p>
                      <p className="text-gray-500">{edu.description}</p>
                    </div>
                  </div>

                  {/* Coursework */}
                  <div className="mb-6">
                    <h4 className="text-lg font-semibold text-gray-900 mb-3">Key Coursework</h4>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                      {edu.coursework.map((course, idx) => (
                        <span
                          key={idx}
                          className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm font-medium"
                        >
                          {course}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Achievements */}
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-3">Key Achievements</h4>
                    <ul className="space-y-2">
                      {edu.achievements.map((achievement, idx) => (
                        <li key={idx} className="flex items-start">
                          <svg className="w-5 h-5 text-primary-500 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                          </svg>
                          <span className="text-gray-600">{achievement}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>

                {/* Side Info */}
                <div className="lg:col-span-1">
                  <div className="bg-gray-50 rounded-lg p-6">
                    <div className="text-center mb-6">
                      <div className="w-20 h-20 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg className="w-10 h-10 text-primary-600" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M12 3L1 9l4 2.18v6L12 21l7-3.82v-6l2-1.09V17h2V9L12 3zm6.82 6L12 12.72 5.18 9 12 5.28 18.82 9zM17 15.99l-5 2.73-5-2.73v-3.72L12 15l5-2.73v3.72z"/>
                        </svg>
                      </div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-2">Academic Details</h4>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <p className="text-sm text-gray-500 mb-1">Duration</p>
                        <p className="font-semibold text-gray-900">{edu.duration}</p>
                      </div>

                      {edu.cgpa && (
                        <div>
                          <p className="text-sm text-gray-500 mb-1">CGPA</p>
                          <p className="font-semibold text-gray-900">{edu.cgpa}</p>
                        </div>
                      )}

                      {edu.percentage && (
                        <div>
                          <p className="text-sm text-gray-500 mb-1">Percentage</p>
                          <p className="font-semibold text-gray-900">{edu.percentage}</p>
                        </div>
                      )}

                      <div>
                        <p className="text-sm text-gray-500 mb-1">Status</p>
                        <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${
                          edu.status.includes('Expected')
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-green-100 text-green-800'
                        }`}>
                          {edu.status}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Resume Download Section */}
        <div className="text-center bg-gradient-to-r from-primary-50 to-primary-100 rounded-2xl p-8 mb-16">
          <div className="max-w-2xl mx-auto">
            <div className="mb-6">
              <div className="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-2">Download My Resume</h3>
              <p className="text-gray-600 leading-relaxed">
                Get a comprehensive overview of my education, experience, skills, and projects in a professionally formatted PDF document.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <a
                href="/Ammar-resume.pdf"
                download="Ammar_Resume.pdf"
                className="inline-flex items-center bg-primary-600 text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-primary-700 transition-all duration-300 transform hover:scale-105 shadow-lg"
              >
                <svg className="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z"/>
                </svg>
                Download Resume
              </a>

              <a
                href="/Ammar-resume.pdf"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center border-2 border-primary-600 text-primary-600 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-primary-600 hover:text-white transition-all duration-300 transform hover:scale-105"
              >
                <svg className="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z"/>
                </svg>
                View Resume
              </a>
            </div>

            <p className="text-sm text-gray-500 mt-4">
              PDF format • Last updated: December 2024
            </p>
          </div>
        </div>


      </div>
    </section>
  );
};

export default Education;
