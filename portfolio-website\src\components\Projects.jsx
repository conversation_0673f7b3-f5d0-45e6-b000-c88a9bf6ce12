const Projects = () => {

  const projects = [
    {
      id: 1,
      title: "Brain Tumor Detection Using AI Transformer",
      category: "ai",
      type: "Final Year Project",
      description: "A lightweight deep learning model for brain tumor segmentation using SegFormer 3D, implemented with Python and PyTorch for high computational efficiency while maintaining accuracy.",
      longDescription: "This project implements a state-of-the-art AI model for medical image analysis, specifically focusing on brain tumor detection and segmentation. The model uses advanced transformer architecture to achieve high accuracy in identifying and segmenting brain tumors from medical imaging data.",
      technologies: ["Python", "PyTorch", "SegFormer 3D", "Deep Learning", "Medical Imaging", "AI/ML"],
      features: [
        "High-accuracy tumor detection and segmentation",
        "Optimized for computational efficiency",
        "Advanced transformer architecture implementation",
        "Medical imaging data processing pipeline"
      ],
      image: "/api/placeholder/600/400",
      demoUrl: "#",
      githubUrl: "#",
      status: "Completed"
    },
    {
      id: 2,
      title: "E-Commerce Website with AI Chatbot",
      category: "fullstack",
      type: "Web Application",
      description: "A MERN stack-based e-commerce platform integrated with an AI Chatbot built using Python (NLP) for product recommendations and query handling.",
      longDescription: "A comprehensive e-commerce solution featuring a modern React frontend, robust Node.js backend, and an intelligent AI chatbot for enhanced customer experience. The platform includes complete shopping functionality with AI-powered product recommendations.",
      technologies: ["React.js", "Node.js", "Express.js", "MongoDB", "Python", "NLP", "AI Chatbot"],
      features: [
        "Complete e-commerce functionality",
        "AI-powered product recommendations",
        "Intelligent customer support chatbot",
        "Secure payment integration",
        "User authentication and authorization",
        "Admin dashboard for inventory management"
      ],
      image: "/api/placeholder/600/400",
      demoUrl: "#",
      githubUrl: "#",
      status: "Completed"
    },
    {
      id: 3,
      title: "Construction Company Website",
      category: "frontend",
      type: "Business Website",
      description: "A Laravel and React.js powered web application for a construction company, featuring project showcase, client contact forms, and database-driven management.",
      longDescription: "A professional business website built for a construction company with modern design, project portfolio showcase, and comprehensive content management system. Features responsive design and optimized performance.",
      technologies: ["Laravel", "React.js", "PHP", "MySQL", "HTML5", "CSS3", "JavaScript"],
      features: [
        "Project portfolio showcase",
        "Client contact and inquiry forms",
        "Database-driven content management",
        "Responsive design for all devices",
        "SEO optimized structure",
        "Admin panel for content updates"
      ],
      image: "/api/placeholder/600/400",
      demoUrl: "#",
      githubUrl: "#",
      status: "Completed"
    },
    {
      id: 4,
      title: "Online Education Website",
      category: "fullstack",
      type: "Academic Project",
      description: "A MySQL and PHP-based project focused on relational database design, implementing features like book issuance records, indexing, and query optimization.",
      longDescription: "An educational platform designed with focus on database architecture and optimization. Features comprehensive course management, student enrollment system, and advanced database operations with performance optimization.",
      technologies: ["PHP", "MySQL", "HTML5", "CSS3", "JavaScript", "Database Design"],
      features: [
        "Course management system",
        "Student enrollment and tracking",
        "Book issuance and library management",
        "Advanced database indexing",
        "Query optimization techniques",
        "Backup & recovery implementation"
      ],
      image: "/api/placeholder/600/400",
      demoUrl: "#",
      githubUrl: "#",
      status: "Completed"
    }
  ];



  return (
    <section id="projects" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">Featured Projects</h2>
          <div className="w-24 h-1 bg-primary-500 mx-auto mb-6"></div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            A showcase of my technical projects demonstrating full-stack development, AI/ML implementation, and database design skills
          </p>
        </div>

        {/* Projects Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {projects.map((project) => (
            <div
              key={project.id}
              className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2"
            >
              {/* Project Image */}
              <div className="relative h-64 bg-gradient-to-br from-primary-400 to-primary-600 flex items-center justify-center">
                <div className="text-white text-center">
                  <svg className="w-16 h-16 mx-auto mb-4 opacity-80" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                  <p className="text-lg font-semibold">{project.title}</p>
                </div>
                <div className="absolute top-4 right-4">
                  <span className="bg-white/20 backdrop-blur-sm text-white px-3 py-1 rounded-full text-sm font-medium">
                    {project.type}
                  </span>
                </div>
              </div>

              {/* Project Content */}
              <div className="p-6">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-xl font-bold text-gray-900">{project.title}</h3>
                  <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                    {project.status}
                  </span>
                </div>

                <p className="text-gray-600 mb-4 leading-relaxed">
                  {project.description}
                </p>

                {/* Technologies */}
                <div className="mb-4">
                  <h4 className="text-sm font-semibold text-gray-900 mb-2">Technologies Used:</h4>
                  <div className="flex flex-wrap gap-2">
                    {project.technologies.map((tech, index) => (
                      <span
                        key={index}
                        className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs font-medium"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Key Features */}
                <div className="mb-6">
                  <h4 className="text-sm font-semibold text-gray-900 mb-2">Key Features:</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    {project.features.slice(0, 3).map((feature, index) => (
                      <li key={index} className="flex items-start">
                        <svg className="w-4 h-4 text-primary-500 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                        </svg>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-4">
                  <a
                    href={project.demoUrl}
                    className="flex-1 bg-primary-600 text-white text-center py-2 px-4 rounded-lg font-medium hover:bg-primary-700 transition-colors duration-300"
                  >
                    View Demo
                  </a>
                  <a
                    href={project.githubUrl}
                    className="flex-1 border border-gray-300 text-gray-700 text-center py-2 px-4 rounded-lg font-medium hover:bg-gray-50 transition-colors duration-300"
                  >
                    View Code
                  </a>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center mt-16">
          <p className="text-lg text-gray-600 mb-6">
            Interested in seeing more of my work or discussing a project?
          </p>
          <a
            href="#contact"
            className="inline-flex items-center bg-primary-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-primary-700 transition-colors duration-300"
          >
            Get In Touch
            <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </a>
        </div>
      </div>
    </section>
  );
};

export default Projects;
