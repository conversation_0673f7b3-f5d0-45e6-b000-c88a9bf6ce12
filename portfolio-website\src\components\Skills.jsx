const Skills = () => {
  const skillCategories = [
    {
      title: "Languages & Markup",
      icon: "💻",
      skills: [
        { name: "JavaScript (ES6+)", level: 90 },
        { name: "Python", level: 85 },
        { name: "<PERSON><PERSON>", level: 80 },
        { name: "HTML5", level: 95 },
        { name: "CSS3", level: 90 }
      ]
    },
    {
      title: "Frameworks & Libraries",
      icon: "⚛️",
      skills: [
        { name: "React.js", level: 88 },
        { name: "Node.js", level: 85 },
        { name: "Express.js", level: 82 },
        { name: "Lara<PERSON>", level: 75 },
        { name: "Flask", level: 70 },
        { name: "FastAPI", level: 50 },
      ]
    },
    {
      title: "Databases",
      icon: "🗄️",
      skills: [
        { name: "MySQL", level: 85 },
        { name: "MongoDB", level: 80 }
      ]
    },
    {
      title: "Tools & Platforms",
      icon: "🛠️",
      skills: [
        { name: "Git/GitHub", level: 90 },
        { name: "<PERSON>er", level: 40 },
        { name: "REST APIs", level: 85 },
        { name: "Postman", level: 70 },
        { name: "MySQL Workbench", level: 85 }
      ]
    },
    {
      title: "Deployment Platforms",
      icon: "🚀",
      skills: [
        { name: "Vercel", level: 85 },
        { name: "Netlify", level: 85 },
        { name: "Railway", level: 80 },
        { name: "Render", level: 80 }
      ]
    },
    {
      title: "Core Skills",
      icon: "🎯",
      skills: [
        { name: "Responsive Web Design", level: 92 },
        { name: "Full-Stack Development", level: 70 },
        { name: "API Integration", level: 85 },
        { name: "Database Design", level: 82 },
        { name: "Problem Solving", level: 90 }
      ]
    }
  ];

  return (
    <section id="skills" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">My Tech Arsenal</h2>
          <div className="w-24 h-1 bg-primary-500 mx-auto mb-6"></div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Here are the technologies and tools I work with to bring ideas to life
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {skillCategories.map((category, categoryIndex) => (
            <div
              key={categoryIndex}
              className="bg-gray-50 rounded-xl p-6 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-2"
            >
              <div className="flex items-center mb-6">
                <span className="text-3xl mr-3">{category.icon}</span>
                <h3 className="text-xl font-semibold text-gray-900">{category.title}</h3>
              </div>

              <div className="space-y-4">
                {category.skills.map((skill, skillIndex) => (
                  <div key={skillIndex} className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-700 font-medium">{skill.name}</span>
                      <span className="text-primary-600 font-semibold">{skill.level}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-primary-500 to-primary-600 h-2 rounded-full transition-all duration-1000 ease-out"
                        style={{ 
                          width: `${skill.level}%`,
                          animationDelay: `${categoryIndex * 0.1 + skillIndex * 0.05}s`
                        }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Additional Skills Section */}
        <div className="mt-16 text-center">
          <h3 className="text-2xl font-semibold text-gray-900 mb-8">Additional Expertise</h3>
          <div className="flex flex-wrap justify-center gap-4">
            {[
              "Responsive Design",
              "Cross-browser Compatibility",
              "Performance Optimization",
              "SEO Best Practices",
              "Agile Development",
              "Code Review",
              "Testing & Debugging",
              "Version Control",
              "CI/CD",
              "Cloud Services"
            ].map((skill, index) => (
              <span
                key={index}
                className="bg-primary-100 text-primary-800 px-4 py-2 rounded-full text-sm font-medium hover:bg-primary-200 transition-colors duration-300"
              >
                {skill}
              </span>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Skills;
