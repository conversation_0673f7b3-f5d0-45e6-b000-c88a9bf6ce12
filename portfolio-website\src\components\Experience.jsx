const Experience = () => {
  const experiences = [
    {
      title: "Web Development Intern",
      company: "Al-Rahim Textile",
      duration: "6 Months",
      period: "2023-2024",
      type: "Internship",
      description: "Gained extensive hands-on experience in full-stack web development",
      responsibilities: [
        "Designed and developed a Customer Complaint Management System using Express.js, Node.js, and MySQL",
        "Built and optimized database schemas, CRUD operations, and indexing strategies",
        "Improved query performance and enhanced data reliability through normalization techniques",
        "Collaborated with team members to deliver high-quality web solutions"
      ],
      technologies: ["Express.js", "Node.js", "MySQL", "JavaScript", "HTML5", "CSS3"],
      achievements: [
        "Successfully delivered a complete complaint management system",
        "Improved database query performance by implementing optimization techniques",
        "Gained practical experience in full-stack development workflow"
      ]
    },
    {
      title: "ICT Intern",
      company: "FFBL Power Company Limited",
      duration: "2 Months",
      period: "2023",
      type: "Internship",
      description: "Focused on server architecture and network infrastructure management",
      responsibilities: [
        "Assisted in server architecture design and deployment using Windows Deployment Server (WDS)",
        "Configured and troubleshooted network infrastructure (switches, routers, access points)",
        "Supported Active Directory administration (user account creation & policy management)",
        "Gained exposure to cloud services and SAP modules for enterprise operations"
      ],
      technologies: ["Windows Server", "Active Directory", "Network Infrastructure", "SAP", "Cloud Services"],
      achievements: [
        "Successfully configured network infrastructure components",
        "Gained valuable experience in enterprise-level IT operations",
        "Developed understanding of server deployment and management"
      ]
    }
  ];

  return (
    <section id="experience" className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">Professional Experience</h2>
          <div className="w-24 h-1 bg-primary-500 mx-auto mb-6"></div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            My journey in the tech industry through internships and hands-on projects
          </p>
        </div>

        <div className="relative">
          {/* Timeline line */}
          <div className="absolute left-1/2 transform -translate-x-1/2 w-1 bg-primary-200 hidden lg:block" style={{height: 'calc(100% - 4rem)'}}></div>

          <div className="space-y-4 lg:space-y-8">
            {experiences.map((exp, index) => (
              <div
                key={index}
                className={`relative flex flex-col lg:flex-row items-center ${
                  index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'
                }`}
              >
                {/* Timeline dot */}
                <div className="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-primary-500 rounded-full border-4 border-white shadow-lg hidden lg:block z-10"></div>

                {/* Content */}
                <div className={`w-full lg:w-5/12 ${index % 2 === 0 ? 'lg:pr-8' : 'lg:pl-8'}`}>
                  <div className="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                    {/* Header */}
                    <div className="mb-6">
                      <div className="flex items-center justify-between mb-2">
                        <span className="bg-primary-100 text-primary-800 px-3 py-1 rounded-full text-sm font-medium">
                          {exp.type}
                        </span>
                        <span className="text-gray-500 font-medium">{exp.period}</span>
                      </div>
                      <h3 className="text-2xl font-bold text-gray-900 mb-1">{exp.title}</h3>
                      <p className="text-xl text-primary-600 font-semibold mb-2">{exp.company}</p>
                      <p className="text-gray-600 mb-4">{exp.description}</p>
                      <div className="flex items-center text-gray-500">
                        <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                        Duration: {exp.duration}
                      </div>
                    </div>

                    {/* Responsibilities */}
                    <div className="mb-6">
                      <h4 className="text-lg font-semibold text-gray-900 mb-3">Key Responsibilities</h4>
                      <ul className="space-y-2">
                        {exp.responsibilities.map((responsibility, idx) => (
                          <li key={idx} className="flex items-start">
                            <svg className="w-5 h-5 text-primary-500 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 24 24">
                              <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                            </svg>
                            <span className="text-gray-600">{responsibility}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Technologies */}
                    <div className="mb-6">
                      <h4 className="text-lg font-semibold text-gray-900 mb-3">Technologies Used</h4>
                      <div className="flex flex-wrap gap-2">
                        {exp.technologies.map((tech, idx) => (
                          <span
                            key={idx}
                            className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm font-medium"
                          >
                            {tech}
                          </span>
                        ))}
                      </div>
                    </div>

                    {/* Achievements */}
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-3">Key Achievements</h4>
                      <ul className="space-y-2">
                        {exp.achievements.map((achievement, idx) => (
                          <li key={idx} className="flex items-start">
                            <svg className="w-5 h-5 text-yellow-500 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 24 24">
                              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                            <span className="text-gray-600">{achievement}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>

                {/* Spacer for the other side */}
                <div className="hidden lg:block w-5/12"></div>
              </div>
            ))}
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center mt-16">
          <p className="text-lg text-gray-600 mb-6">
            Looking for opportunities to contribute to innovative projects and grow professionally
          </p>
          <a
            href="#contact"
            className="inline-flex items-center bg-primary-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-primary-700 transition-colors duration-300"
          >
            Let's Work Together
            <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </a>
        </div>
      </div>
    </section>
  );
};

export default Experience;
